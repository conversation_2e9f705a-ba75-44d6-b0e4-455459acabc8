import os
from dotenv import load_dotenv
from telethon.tl.types import InputPhoto

# Load environment variables
load_dotenv()

# Telegram API credentials
API_ID = int(os.getenv('API_ID'))
API_HASH = os.getenv('API_HASH')
BOT_TOKEN = os.getenv('BOT_TOKEN')

# Solana RPC endpoints
SOLANA_MAINNET_RPC = "https://rpc.helius.xyz/?api-key=0ab09b7f-55c0-4f5c-9af5-7726d88bd5c9"

# Wallet private keys
ESCROW_PRIVATE_KEY = "4u7RnPTs3A9osthbku5juATf8Z1An7J5E5mn8fNjCechXYFP7mHwdbXgJ2omiJxBjeVoPw3NCWMn6mU8jTzYykyC"
WORLD_PRIVATE_KEY = "4TzEoYWtkAM8wozMVcvL6mFCPiMVJMcpQPd3yStxnzMP6APCzabKBKK2uuWyiNvUjoxSKZZgtSFnZacSJL1KcZJS"
HOUSE_PRIVATE_KEY = "4dUXmQsRoyxRAdq4We6pSETHG57hgt7oio7YcpV2J7GTrFuCdqQuwpMhMjtTtdDsj3bcpQxqeQEgPkyhRjGAjUzr"

# Jupiter API for token prices
RAYDIUM_API_URL = "https://api-v3.raydium.io/mint/price"

# Open Exchange Rates API for currency conversion
OPEN_ER_API_URL = "https://open.er-api.com/v6/latest/USD"

# Token configuration
# This dictionary contains all possible tokens that could be supported
# To change the active token, just change the ACTIVE_TOKEN value below
ALL_TOKENS = {
    "BONK": {
        "address": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
        "name": "BONK",
        "symbol": "BONK",
        "decimals": 5,
        "default_bet_amount": 1000  # Default bet amount for this token
    }
    # Add more tokens here in the future if needed
}

# Set the active token - change this value to switch tokens
ACTIVE_TOKEN = "BONK"

# Create a dictionary with only the active token for compatibility
SUPPORTED_TOKENS = {ACTIVE_TOKEN: ALL_TOKENS[ACTIVE_TOKEN]}

# Token constants for easy access
TOKEN_ADDRESS = ALL_TOKENS[ACTIVE_TOKEN]["address"]
TOKEN_SYMBOL = ALL_TOKENS[ACTIVE_TOKEN]["symbol"]
TOKEN_DECIMALS = ALL_TOKENS[ACTIVE_TOKEN]["decimals"]
DEFAULT_BET_AMOUNT = ALL_TOKENS[ACTIVE_TOKEN]["default_bet_amount"]

# SOL address for transaction fees (hardcoded)
SOL_ADDRESS = "So11111111111111111111111111111111111111112"

# Logging configuration
LOG_PRICE_UPDATES = False  # Set to True to enable detailed price update logs

# MongoDB connection
MONGO_URI = "mongodb://localhost:27017"
DB_NAME = "solette"

MIN_BET_USD = 0.01
MAX_BET_USD = 1000

MIN_WITHDRAWAL_USD = 0.01
MAX_WITHDRAWAL_USD = 1000

DEFAULT_BET_USD = 1.0

# Withdrawal settings
WITHDRAWALS_ENABLED = True  # Global switch to enable/disable withdrawals
WITHDRAWAL_FEE_PERCENT = 2.0  # Fee percentage for withdrawals (e.g., 2.0 = 2%)

# Deposit API settings
DEPOSIT_API_URL = "https://solette-deposit.vercel.app/api/deposits?processed=false"
DEPOSIT_UPDATE_URL = "https://solette-deposit.vercel.app/api/deposits/update"
DEPOSIT_CHECK_INTERVAL = 3  # Check for deposits every 2 seconds

# Game types and titles
GAME_TYPE_RR = "russian_roulette"
GAME_TYPE_EMOJI = "telegram_emoji"

GAME_TITLES = {
    GAME_TYPE_RR: "Russian Roulette",
    GAME_TYPE_EMOJI: "Emoji Battle"
}

# Constants for stored photos
RR_PHOTO = {
    'id': 5242202361991131317,
    'access_hash': 5581498333348095460,
    'dc_id': 2
}

# Games menu photo (using the same photo for now)
GAMES_PHOTO = {
    'id': 5247156108550861398,
    'access_hash': 3452211221212395091,
    'dc_id': 2
}

# Emoji game photo
EMOJI_PHOTO = {
    'id': 5247156108550861414,
    'access_hash': -3826520967210688893,
    'dc_id': 2
}

# Game-specific photos
GAME_PHOTOS = {
    GAME_TYPE_RR: RR_PHOTO,
    GAME_TYPE_EMOJI: EMOJI_PHOTO
}

# Telegram Emoji game configuration
EMOJI_GAME_EMOJIS = ["🎲", "🎳", "🎯", "🏀", "⚽"]

# Max players for each emoji type
EMOJI_MAX_PLAYERS = {
    "🎲": 5,  # Regular dice game
    "🎯": 3,  # Darts
    "🎳": 3,  # Bowling
    "🏀": 2,  # Basketball
    "⚽": 2   # Football/Soccer
}

# Number of throws for each emoji type
EMOJI_THROWS = {
    "🎲": 2,  # Regular dice game - 2 throws
    "🎯": 3,  # Darts - 3 throws
    "🎳": 3,  # Bowling - 3 throws
    "🏀": 3,  # Basketball - 3 throws
    "⚽": 3   # Football/Soccer - 3 throws
}

WALLET_PHOTO = {
    'id': 5242469852554325325,
    'access_hash': -5151697987847310346,
    'dc_id': 2
}

PROFILE_PHOTO = {
    'id': 5242469852554325328,
    'access_hash': -468215219882699835,
    'dc_id': 2
}

# List of supported currencies with their labels
SUPPORTED_CURRENCIES = {
    "USD": {"label": "United States Dollar", "symbol": "$"},
    "EUR": {"label": "Euro", "symbol": "€"},
    "JPY": {"label": "Japanese Yen", "symbol": "¥"},
    "INR": {"label": "Indian Rupee", "symbol": "₹"},
    "ARS": {"label": "Argentine peso", "symbol": "none"},
    "BRL": {"label": "Brazilian Real", "symbol": "R$"},
    "CAD": {"label": "Canadian Dollar", "symbol": "CA$"},
    "CLP": {"label": "Chilean Peso", "symbol": "none"},
    "CNY": {"label": "Chinese Yuan", "symbol": "CN¥"},
    "IDR": {"label": "Indonesian Rupiah", "symbol": "none"},
    "KRW": {"label": "South Korean Won", "symbol": "₩"},
    "MXN": {"label": "Mexican Peso", "symbol": "MX$"},
    "NGN": {"label": "Nigerian naira", "symbol": "₦"},
    "PEN": {"label": "Peruvian Sol", "symbol": "none"},
    "PHP": {"label": "Philippine Peso", "symbol": "₱"},
    "PLN": {"label": "Polish Złoty", "symbol": "zł"},
    "RUB": {"label": "Russian Ruble", "symbol": "₽"},
    "TRY": {"label": "Turkish Lira", "symbol": "₺"},
    "VND": {"label": "Vietnamese Dong", "symbol": "₫"},
    "UAH": {"label": "Ukrainian hryvnia", "symbol": "₴"}
}

# Currency flags mapping
CURRENCY_FLAGS = {
    "USD": "🇺🇸",
    "EUR": "🇪🇺",
    "JPY": "🇯🇵",
    "INR": "🇮🇳",
    "ARS": "🇦🇷",
    "BRL": "🇧🇷",
    "CAD": "🇨🇦",
    "CLP": "🇨🇱",
    "CNY": "🇨🇳",
    "IDR": "🇮🇩",
    "KRW": "🇰🇷",
    "MXN": "🇲🇽",
    "NGN": "🇳🇬",
    "PEN": "🇵🇪",
    "PHP": "🇵🇭",
    "PLN": "🇵🇱",
    "RUB": "🇷🇺",
    "TRY": "🇹🇷",
    "VND": "🇻🇳",
    "UAH": "🇺🇦"
}

# Support group configuration
SUPPORT_GROUP_ID = -1002603334444
SUPPORT_TOPIC_IDS = {
    "suggestions": 6,
    "bugs": 9,
    "support": 16,
    "swaps": 2,
    "withdrawals": 4
}