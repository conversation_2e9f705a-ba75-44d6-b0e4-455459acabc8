from solana.rpc.async_api import AsyncClient
from solders.keypair import Keypair # type: ignore
from solders.pubkey import Pubkey # type: ignore
from solders.transaction import Transaction
from solders.message import Message # type: ignore
from solders.hash import Hash # type: ignore
from solders.instruction import Instruction as SoldersInstruction, AccountMeta # type: ignore
from solders.compute_budget import set_compute_unit_price # type: ignore
from solana.rpc.types import TxOpts
from solana.rpc.commitment import Confirmed
from spl.token.client import Token
from spl.token.instructions import transfer_checked, TransferCheckedParams, create_associated_token_account
from spl.token.instructions import initialize_mint, mint_to, MintToParams, InitializeMintParams
from spl.token.constants import TOKEN_PROGRAM_ID, ASSOCIATED_TOKEN_PROGRAM_ID
from solders.transaction import VersionedTransaction # type: ignore
from solders.address_lookup_table_account import AddressLookupTableAccount # type: ignore
from solders.message import MessageV0 # type: ignore
import base58
import logging
import asyncio
import datetime
import aiohttp
import base64
import uuid
import time
import requests
from typing import List, Dict, Tuple, Optional, Any, Union, Callable
from config import (WIT<PERSON>RAWALS_ENABLED, WITHDRAWAL_FEE_PERCENT, TOKEN_SYMBOL,
                   ESCROW_PRIVATE_KEY, WORLD_PRIVATE_KEY, HOUSE_PRIVATE_KEY)

# Constants for token swap
ACTIVE_TOKEN = TOKEN_SYMBOL  # Use the active token from config
SOL_ADDRESS = "So********************************111111112"  # SOL mint address

# Memo program ID
MEMO_PROGRAM_ID = "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"

# Configure logging
logger = logging.getLogger(__name__)

# Define a simple logger function that uses the logger directly
def log_message(level, message):
    if level == "INFO":
        logger.info(message)
    elif level == "WARNING":
        logger.warning(message)
    elif level == "ERROR":
        logger.error(message)
    elif level == "DEBUG":
        logger.debug(message)

# Connect to mainnet Solana using Staked Helius RPC
STAKED_HELIUS_RPC = "https://staked.helius-rpc.com?api-key=27fd6baa-75e9-4d39-9832-d5a43419ad78"
mainnet_client = AsyncClient(STAKED_HELIUS_RPC, commitment=Confirmed)

# ESCROW wallet keypair
ESCROW_KEYPAIR = None

# Global variables for wallet keypairs
WORLD_KEYPAIR = None
HOUSE_KEYPAIR = None

# Global dictionary to store token mints
TOKEN_MINTS = {}

# MongoDB client will be set from bot.py
db = None
wallets_collection = None
withdrawals_collection = None

# Callback function for broadcasting deposits
# This will be set from bot.py
broadcast_deposit_callback = None

# Initialize database connection
def init_db(database, deposit_callback=None):
    """
    Initialize database connection and set callback function

    Args:
        database: MongoDB database instance
        deposit_callback: Optional callback function for broadcasting deposits
    """
    global db, wallets_collection, withdrawals_collection, broadcast_deposit_callback, ESCROW_KEYPAIR, WORLD_KEYPAIR, HOUSE_KEYPAIR
    db = database
    wallets_collection = db["wallets"]
    withdrawals_collection = db["withdrawals"]

    # Set the callback function if provided
    if deposit_callback:
        broadcast_deposit_callback = deposit_callback

    # Initialize wallet keypairs from config
    ESCROW_KEYPAIR = Keypair.from_base58_string(ESCROW_PRIVATE_KEY)
    log_message("INFO", f"Initialized ESCROW wallet: {ESCROW_KEYPAIR.pubkey()}")

    WORLD_KEYPAIR = Keypair.from_base58_string(WORLD_PRIVATE_KEY)
    log_message("INFO", f"Initialized WORLD wallet: {WORLD_KEYPAIR.pubkey()}")

    HOUSE_KEYPAIR = Keypair.from_base58_string(HOUSE_PRIVATE_KEY)
    log_message("INFO", f"Initialized HOUSE wallet: {HOUSE_KEYPAIR.pubkey()}")

    log_message("INFO", "Solana utils database connection initialized")

async def get_escrow_wallet() -> Tuple[Keypair, Pubkey]:
    """
    Get the ESCROW wallet keypair and pubkey

    Returns:
        Tuple of (Keypair, Pubkey)
    """
    global ESCROW_KEYPAIR

    if ESCROW_KEYPAIR is None:
        # Initialize ESCROW wallet keypair if not already done
        ESCROW_KEYPAIR = Keypair.from_base58_string(ESCROW_PRIVATE_KEY)
        log_message("INFO", f"Initialized ESCROW wallet: {ESCROW_KEYPAIR.pubkey()}")

    return ESCROW_KEYPAIR, ESCROW_KEYPAIR.pubkey()

# Function to derive ATA
def get_ata(pubkey: Pubkey, mint: Pubkey) -> Pubkey:
    """Get the associated token account address for a wallet and token mint"""
    ata, _ = Pubkey.find_program_address(
        seeds=[bytes(pubkey), bytes(TOKEN_PROGRAM_ID), bytes(mint)],
        program_id=ASSOCIATED_TOKEN_PROGRAM_ID
    )
    return ata

async def create_keypair() -> Keypair:
    """Create a new Solana keypair"""
    return Keypair()

# Add a method to Keypair class to convert to base58 string
def keypair_to_base58_string(keypair: Keypair) -> str:
    """Convert a Keypair to a base58 string"""
    return base58.b58encode(bytes(keypair)).decode('ascii')

def create_memo_instruction(memo_text: str, signer_pubkey: Pubkey) -> SoldersInstruction:
    """
    Create a memo instruction with the given text and signer

    Args:
        memo_text: Text to include in the memo
        signer_pubkey: Public key of the signer

    Returns:
        SoldersInstruction: The memo instruction
    """
    memo_program_id = Pubkey.from_string(MEMO_PROGRAM_ID)

    # Create the memo instruction
    memo_ix = SoldersInstruction(
        program_id=memo_program_id,
        accounts=[
            AccountMeta(pubkey=signer_pubkey, is_signer=True, is_writable=False)
        ],
        data=memo_text.encode('utf-8')
    )

    return memo_ix

async def get_or_create_wallet(wallet_type: str) -> Tuple[Keypair, Pubkey]:
    """
    Get or create a wallet of the specified type (WORLD or HOUSE)

    Args:
        wallet_type: The type of wallet to get or create (WORLD or HOUSE)

    Returns:
        Tuple of (Keypair, Pubkey)
    """
    global WORLD_KEYPAIR, HOUSE_KEYPAIR

    # Use private keys from config.py
    if wallet_type == "WORLD":
        if WORLD_KEYPAIR is None:
            WORLD_KEYPAIR = Keypair.from_base58_string(WORLD_PRIVATE_KEY)
            log_message("INFO", f"Initialized WORLD wallet from config: {WORLD_KEYPAIR.pubkey()}")
        return WORLD_KEYPAIR, WORLD_KEYPAIR.pubkey()
    elif wallet_type == "HOUSE":
        if HOUSE_KEYPAIR is None:
            HOUSE_KEYPAIR = Keypair.from_base58_string(HOUSE_PRIVATE_KEY)
            log_message("INFO", f"Initialized HOUSE wallet from config: {HOUSE_KEYPAIR.pubkey()}")
        return HOUSE_KEYPAIR, HOUSE_KEYPAIR.pubkey()
    else:
        # For any other wallet type, use the database (should not happen)
        log_message("WARNING", f"Unexpected wallet type: {wallet_type}, using database lookup")

        # Check if wallet exists in database
        wallet_doc = await wallets_collection.find_one({"type": wallet_type})

        if wallet_doc and "private_key" in wallet_doc:
            # Wallet exists, load it
            keypair = Keypair.from_base58_string(wallet_doc["private_key"])
            pubkey = keypair.pubkey()
            log_message("INFO", f"Loaded existing {wallet_type} wallet: {pubkey}")
        else:
            # Create new wallet
            keypair = await create_keypair()
            pubkey = keypair.pubkey()

            # Save to database
            await wallets_collection.update_one(
                {"type": wallet_type},
                {"$set": {
                    "type": wallet_type,
                    "address": str(pubkey),
                    "private_key": keypair_to_base58_string(keypair),
                    "created_at": datetime.datetime.now()
                }},
                upsert=True
            )
            log_message("INFO", f"Created new {wallet_type} wallet: {pubkey}")

        return keypair, pubkey

async def create_lobby_wallet(lobby_id, token_symbol=None) -> Dict:
    """
    This function is now a no-op since we're not creating lobby wallets anymore.
    Lobbies are now offchain only, and all tokens are sent to the ESCROW wallet.

    Args:
        lobby_id: The ID of the lobby
        token_symbol: Token symbol to create account for (optional)

    Returns:
        Dict with placeholder wallet information
    """
    log_message("INFO", f"create_lobby_wallet is now a no-op, using ESCROW wallet for all lobbies")

    # Return a placeholder wallet info with the ESCROW wallet
    escrow_keypair, escrow_pubkey = await get_escrow_wallet()

    wallet_info = {
        "type": "ESCROW",
        "lobby_id": str(lobby_id),
        "address": str(escrow_pubkey),
        "created_at": datetime.datetime.now()
    }

    return wallet_info

async def get_token_balance(wallet_address: str, token_symbol: str = None) -> float:
    """
    Get token balance for a wallet address

    Args:
        wallet_address: The wallet address to check
        token_symbol: Optional token symbol (e.g., "SOL", "BONK"). If not provided, uses the default token.

    Returns:
        float: The token balance
    """
    try:
        # Get token mint from symbol
        if token_symbol is None:
            token_symbol = "BONK"  # Default token

        # Get token info from database
        token_info = await db["tokens"].find_one({"symbol": token_symbol})
        if not token_info:
            log_message("ERROR", f"Token {token_symbol} not found in database")
            return 0

        # Get token mint address (always use mainnet address)
        mint = Pubkey.from_string(token_info["address"])

        # Store in global dictionary for future use
        TOKEN_MINTS[token_symbol] = mint

        pubkey = Pubkey.from_string(wallet_address)
        ata = get_ata(pubkey, mint)

        # First check if the token account exists - use Confirmed commitment
        account_info = await mainnet_client.get_account_info(ata, commitment=Confirmed)
        if not account_info.value:
            # Token account doesn't exist, which means balance is 0
            return 0

        # Get account balance - use Confirmed commitment
        balance_response = await mainnet_client.get_token_account_balance(ata, commitment=Confirmed)
        if hasattr(balance_response, 'value') and balance_response.value:
            ui_amount = balance_response.value.ui_amount
            if ui_amount is not None:
                # Round to 2 decimal places for simplicity and readability
                return round(float(ui_amount), 2)

            # If ui_amount is None, calculate from raw amount
            # Get token decimals for the specific token
            token_info = await db["tokens"].find_one({"symbol": token_symbol})
            decimals = token_info.get("decimals", 9) if token_info else 9

            amount = float(balance_response.value.amount) / (10 ** decimals)
            # Round to 2 decimal places for simplicity and readability
            return round(amount, 2)
        return 0
    except Exception as e:
        log_message("ERROR", f"Error getting token balance: {str(e)}")
        # For debugging
        log_message("ERROR", f"Wallet: {wallet_address}, Token: {token_symbol}, Error type: {type(e)}")
        return 0



async def create_token(token_symbol: str, token_name: str, decimals: int) -> Tuple[Pubkey, bool]:
    """
    Create a new token on the local Solana network

    Args:
        token_symbol: Symbol of the token (e.g., "SOL", "BONK")
        token_name: Name of the token (e.g., "Solana", "BONK")
        decimals: Number of decimals for the token

    Returns:
        Tuple of (token mint pubkey, success boolean)
    """
    try:
        # Get WORLD wallet for creating tokens
        world_keypair, _ = await get_or_create_wallet("WORLD")

        # Create a new keypair for the token mint
        mint_keypair = await create_keypair()
        mint_pubkey = mint_keypair.pubkey()

        # Create transaction
        tx = Transaction()

        # Add initialize mint instruction
        create_mint_ix = initialize_mint(
            InitializeMintParams(
                program_id=TOKEN_PROGRAM_ID,
                mint=mint_pubkey,
                mint_authority=world_keypair.pubkey(),
                freeze_authority=world_keypair.pubkey(),
                decimals=decimals
            )
        )
        tx.add(create_mint_ix)

        # Get recent blockhash
        blockhash_resp = await mainnet_client.get_latest_blockhash()
        blockhash = blockhash_resp.value.blockhash
        tx.recent_blockhash = blockhash

        # Sign transaction
        tx.sign(world_keypair, mint_keypair)

        # Send transaction
        response = await mainnet_client.send_raw_transaction(
            tx.serialize(),
            opts=TxOpts(
                skip_preflight=False,
                max_retries=10,
                preflight_commitment=Confirmed,
            )
        )
        tx_signature = response.value

        # Wait for confirmation
        confirmation = await mainnet_client.confirm_transaction(
            tx_signature,
            commitment=Confirmed,
        )

        if confirmation.value:
            # Store token info in database
            await db["tokens"].update_one(
                {"symbol": token_symbol},
                {"$set": {
                    "symbol": token_symbol,
                    "name": token_name,
                    "localnet_address": str(mint_pubkey),
                    "decimals": decimals,
                    "created_at": datetime.datetime.now()
                }},
                upsert=True
            )

            # Store in global dictionary
            TOKEN_MINTS[token_symbol] = mint_pubkey

            log_message("INFO", f"Created token {token_name} ({token_symbol}) with mint {mint_pubkey}")
            return mint_pubkey, True
        else:
            log_message("ERROR", f"Token creation not confirmed")
            return mint_pubkey, False
    except Exception as e:
        log_message("ERROR", f"Error creating token: {str(e)}")
        return None, False

async def mint_tokens(token_symbol: str, recipient_address: str, amount: float) -> bool:
    """
    Mint tokens to a recipient

    Args:
        token_symbol: Symbol of the token to mint
        recipient_address: Recipient's wallet address
        amount: Amount of tokens to mint

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get token mint
        token_info = await db["tokens"].find_one({"symbol": token_symbol})
        if not token_info:
            log_message("ERROR", f"Token {token_symbol} not found")
            return False

        # Use localnet_address if available, otherwise use address
        if "localnet_address" in token_info and token_info["localnet_address"]:
            mint_pubkey = Pubkey.from_string(token_info["localnet_address"])
        else:
            mint_pubkey = Pubkey.from_string(token_info["address"])

        decimals = token_info["decimals"]

        # Get WORLD wallet (mint authority)
        world_keypair, _ = await get_or_create_wallet("WORLD")

        # Convert amount to token units
        amount_units = int(amount * (10 ** decimals))

        # Get recipient's public key
        recipient_pubkey = Pubkey.from_string(recipient_address)

        # Create transaction
        tx = Transaction()

        # Create associated token account if it doesn't exist
        try:
            recipient_token_account = get_ata(recipient_pubkey, mint_pubkey)
            account_info = await mainnet_client.get_account_info(recipient_token_account)

            if not account_info.value:
                # Add instruction to create the token account
                create_ata_ix = create_associated_token_account(
                    payer=world_keypair.pubkey(),
                    owner=recipient_pubkey,
                    mint=mint_pubkey
                )
                tx.add(create_ata_ix)

            # Add mint instruction
            mint_ix = mint_to(
                MintToParams(
                    program_id=TOKEN_PROGRAM_ID,
                    mint=mint_pubkey,
                    dest=recipient_token_account,
                    mint_authority=world_keypair.pubkey(),
                    amount=amount_units,
                    signers=[]
                )
            )
            tx.add(mint_ix)

            # Get recent blockhash
            blockhash_resp = await mainnet_client.get_latest_blockhash()
            blockhash = blockhash_resp.value.blockhash
            tx.recent_blockhash = blockhash

            # Sign transaction
            tx.sign(world_keypair)

            # Send transaction
            response = await mainnet_client.send_raw_transaction(
                tx.serialize(),
                opts=TxOpts(
                    skip_preflight=False,
                    max_retries=10,
                    preflight_commitment=Confirmed,
                )
            )
            tx_signature = response.value

            # Wait for confirmation
            confirmation = await mainnet_client.confirm_transaction(
                tx_signature,
                commitment=Confirmed,
            )

            if not confirmation.value:
                log_message("ERROR", f"Mint transaction not confirmed")
                return False

            log_message("INFO", f"Minted {amount} {token_symbol} to {recipient_address}")
            return True
        except Exception as mint_error:
            log_message("ERROR", f"Error in mint operation: {str(mint_error)}")
            return False
    except Exception as e:
        log_message("ERROR", f"Error minting tokens: {str(e)}")
        return False

async def initialize_tokens():
    """Initialize tokens on the local Solana network and load mainnet wallet"""
    try:
        # Get WORLD wallet
        _, world_pubkey = await get_or_create_wallet("WORLD")

        # Get HOUSE wallet
        _, house_pubkey = await get_or_create_wallet("HOUSE")

        # Import config for active token
        from config import ACTIVE_TOKEN, ALL_TOKENS

        # Check if active token exists in database
        token_symbol = ACTIVE_TOKEN
        token_info = await db["tokens"].find_one({"symbol": token_symbol})

        if token_info and "localnet_address" in token_info and token_info["localnet_address"]:
            # Use the existing localnet address
            TOKEN_MINTS[token_symbol] = Pubkey.from_string(token_info["localnet_address"])
            log_message("INFO", f"Loaded existing token {token_symbol} with localnet mint {token_info['localnet_address']}")
        else:
            # Get token details from config
            token_config = ALL_TOKENS[token_symbol]

            # Create a new token on the local network
            mint_pubkey, success = await create_token(
                token_symbol=token_symbol,
                token_name=token_config["name"],
                decimals=token_config["decimals"]
            )

            if success:
                # Update the token document with the localnet address
                await db["tokens"].update_one(
                    {"symbol": token_symbol},
                    {"$set": {
                        "symbol": token_symbol,
                        "name": token_config["name"],
                        "address": token_config["address"],
                        "decimals": token_config["decimals"],
                        "localnet_address": str(mint_pubkey),
                        "created_at": datetime.datetime.now()
                    }},
                    upsert=True
                )

                # Store in global dictionary
                TOKEN_MINTS[token_symbol] = mint_pubkey

                # Mint initial supply to WORLD wallet
                initial_supply = 1_000_000_000  # 1 billion tokens
                await mint_tokens(token_symbol, str(world_pubkey), initial_supply)

                # Mint some tokens to HOUSE wallet
                await mint_tokens(token_symbol, str(house_pubkey), initial_supply / 10)  # 100 million tokens

                log_message("INFO", f"Created new token {token_symbol} with localnet mint {mint_pubkey}")
            else:
                log_message("ERROR", f"Failed to create token {token_symbol}")

        # WORLD wallet is already loaded and will be used for both localnet and mainnet

        log_message("INFO", "Token initialization complete")
        return True
    except Exception as e:
        log_message("ERROR", f"Error initializing tokens: {str(e)}")
        return False



async def send_and_confirm_transaction(instructions, signers):
    """
    Sign and send a transaction on localnet with proper priority fee estimation

    Args:
        instructions: List of instructions to include in the transaction
        signers: List of keypairs to sign the transaction

    Returns:
        Tuple of (signature, success)
    """
    try:
        # Check if compute unit price instruction is already in the list
        compute_budget_program_id = Pubkey.from_string("ComputeBudget111111111111111111111111111111")
        has_compute_price = False

        for ix in instructions:
            if hasattr(ix, 'program_id') and ix.program_id == compute_budget_program_id:
                # Check instruction data to determine if it's a compute price instruction
                if len(ix.data) > 0 and ix.data[0] == 1:  # ComputeBudgetInstruction::SetComputeUnitPrice
                    has_compute_price = True

        # Convert all instructions to solders format
        solders_instructions = []

        # Get priority fee estimate from Helius API
        priority_fee = await get_priority_fee_estimate()

        # Add compute unit price instruction if not already present
        if not has_compute_price:
            compute_price_ix = set_compute_unit_price(priority_fee)
            solders_instructions.append(compute_price_ix)
            log_message("INFO", f"Using priority fee: {priority_fee}")

        # Add the rest of the instructions
        for instruction in instructions:
            if not isinstance(instruction, SoldersInstruction):
                # Extract program_id, accounts, and data
                program_id = instruction.program_id
                keys = [
                    AccountMeta(
                        pubkey=account_meta.pubkey,
                        is_signer=account_meta.is_signer,
                        is_writable=account_meta.is_writable
                    )
                    for account_meta in instruction.keys
                ]
                data = instruction.data
                # Create solders instruction
                instruction = SoldersInstruction(program_id, data, keys)
            solders_instructions.append(instruction)

        # Get recent blockhash
        blockhash_resp = await mainnet_client.get_latest_blockhash()
        blockhash = Hash.from_string(str(blockhash_resp.value.blockhash))

        # Create message and transaction
        message = Message.new_with_blockhash(
            solders_instructions,
            signers[0].pubkey(),  # Fee payer
            blockhash
        )

        tx = Transaction(signers, message, blockhash)

        # Send transaction with maxRetries=0 as requested
        response = await mainnet_client.send_raw_transaction(
            bytes(tx),
            opts=TxOpts(skip_preflight=False, max_retries=0, preflight_commitment=Confirmed)
        )
        tx_signature = response.value

        # Wait for confirmation
        confirmation = await mainnet_client.confirm_transaction(tx_signature, commitment=Confirmed)

        if confirmation.value:
            log_message("INFO", f"Transaction confirmed: {tx_signature}")
            return str(tx_signature), True
        else:
            log_message("ERROR", f"Transaction not confirmed: {tx_signature}")
            return str(tx_signature), False
    except Exception as e:
        log_message("ERROR", f"Error sending transaction: {str(e)}")
        return str(e), False

# This function has been moved to line ~1506 with additional parameters



async def collect_bets_to_escrow(token_symbol: str, players: List[Dict], lobby_id: str, game_type: str = None, game_id: str = None) -> Tuple[str, bool]:
    """
    Collect bets from all players and send them to the ESCROW wallet

    Args:
        token_symbol: Token symbol (e.g., "SOL", "BONK")
        players: List of dicts with player wallet addresses and bet amounts
                [{"wallet_address": "address", "private_key": "key", "amount": amount}, ...]
        lobby_id: The ID of the lobby (for tracking purposes)
        game_type: Type of game (e.g., "russian_roulette", "telegram_emoji")
        game_id: ID of the game

    Returns:
        Tuple of (signature, success)
    """
    try:
        # Get token info
        token_info = await db["tokens"].find_one({"symbol": token_symbol})
        if not token_info:
            log_message("ERROR", f"Token {token_symbol} not found")
            return "", False

        # Get token mint address (always use mainnet address)
        mint_pubkey = Pubkey.from_string(token_info["address"])
        decimals = token_info["decimals"]

        # Get WORLD wallet for fee payment
        world_keypair, _ = await get_or_create_wallet("WORLD")

        # Get ESCROW wallet
        escrow_keypair, escrow_pubkey = await get_escrow_wallet()
        escrow_token_account = get_ata(escrow_pubkey, mint_pubkey)

        # Create instruction list
        instructions = []

        # Add memo instruction if game type and ID are provided
        if game_type and game_id:
            # Create memo with format "{game_type}|{game_id}|s" (s for start)
            memo_text = f"{game_type}|{game_id}|s"
            memo_ix = create_memo_instruction(memo_text, world_keypair.pubkey())
            instructions.append(memo_ix)
            log_message("INFO", f"Added memo instruction with text: {memo_text}")

        # Check if ESCROW token account exists
        escrow_account_info = await mainnet_client.get_account_info(escrow_token_account)
        if not escrow_account_info.value:
            # Add instruction to create the token account
            create_escrow_ata_ix = create_associated_token_account(
                payer=world_keypair.pubkey(),
                owner=escrow_pubkey,
                mint=mint_pubkey
            )
            instructions.append(create_escrow_ata_ix)

        # Collect all player keypairs for signing
        signers = [world_keypair]

        # Track created ATAs to avoid duplicates
        created_atas = set()

        # Add transfer instructions for each player
        for player in players:
            player_address = player.get("wallet_address")
            player_private_key = player.get("private_key")
            bet_amount = player.get("amount", 0)

            if not player_address or not player_private_key or bet_amount <= 0:
                continue

            # Convert amount to token units
            amount_units = int(bet_amount * (10 ** decimals))

            # Get player's keypair
            player_keypair = Keypair.from_base58_string(player_private_key)
            signers.append(player_keypair)

            # Get token accounts
            player_pubkey = Pubkey.from_string(player_address)
            player_token_account = get_ata(player_pubkey, mint_pubkey)

            # Check if player token account exists and hasn't been created in this transaction
            player_account_info = await mainnet_client.get_account_info(player_token_account)
            if not player_account_info.value and str(player_token_account) not in created_atas:
                # Add instruction to create the token account
                create_player_ata_ix = create_associated_token_account(
                    payer=world_keypair.pubkey(),
                    owner=player_pubkey,
                    mint=mint_pubkey
                )
                instructions.append(create_player_ata_ix)
                created_atas.add(str(player_token_account))

            # Add transfer instruction to send tokens to ESCROW wallet
            transfer_ix = transfer_checked(
                TransferCheckedParams(
                    program_id=TOKEN_PROGRAM_ID,
                    source=player_token_account,
                    mint=mint_pubkey,
                    dest=escrow_token_account,
                    owner=player_pubkey,
                    amount=amount_units,
                    decimals=decimals,
                    signers=[]
                )
            )
            instructions.append(transfer_ix)

        # Send transaction if we have instructions
        if len(instructions) > 1:  # More than just the compute budget instruction
            log_message("INFO", f"Collecting bets from {len(players)} players to ESCROW wallet for lobby {lobby_id}")
            tx_signature, success = await send_and_confirm_transaction_mainnet(instructions, signers)

            if success:
                log_message("INFO", f"Successfully collected bets to ESCROW. Signature: {tx_signature}")

                # Record the transaction in the database
                transaction_record = {
                    "type": "escrow_deposit",
                    "lobby_id": lobby_id,
                    "token_symbol": token_symbol,
                    "players": [{"wallet_address": p.get("wallet_address"), "amount": p.get("amount", 0)} for p in players],
                    "tx_signature": tx_signature,
                    "created_at": datetime.datetime.now(),
                    "status": "completed"
                }

                # Insert into the database
                await db["lobby_transactions"].insert_one(transaction_record)
            else:
                log_message("ERROR", f"Failed to collect bets to ESCROW. Signature: {tx_signature}")

            return tx_signature, success
        else:
            log_message("WARNING", "No transfer instructions to process")
            return "", True  # Return success if there's nothing to do
    except Exception as e:
        log_message("ERROR", f"Error collecting bets to ESCROW: {str(e)}")
        return "", False

async def distribute_from_escrow(token_symbol: str, winners: List[Dict], lobby_id: str, revenue_amount: float = 0, game_type: str = None, game_id: str = None) -> Tuple[str, bool]:
    """
    Distribute tokens from the ESCROW wallet to winners with 5% fee to HOUSE wallet

    Args:
        token_symbol: Token symbol (e.g., "SOL", "BONK")
        winners: List of winner wallet addresses and amounts
                [{"wallet_address": "address", "amount": amount}, ...]
        lobby_id: The ID of the lobby (for tracking purposes)
        revenue_amount: Amount for revenue (default: 0)
        game_type: Type of game (e.g., "russian_roulette", "telegram_emoji")
        game_id: ID of the game

    Returns:
        Tuple of (signature, success)
    """
    try:
        # Get token info
        token_info = await db["tokens"].find_one({"symbol": token_symbol})
        if not token_info:
            log_message("ERROR", f"Token {token_symbol} not found")
            return "", False

        # Get token mint address (always use mainnet address)
        mint_pubkey = Pubkey.from_string(token_info["address"])
        decimals = token_info["decimals"]

        # Get ESCROW wallet
        escrow_keypair, escrow_pubkey = await get_escrow_wallet()

        # Get HOUSE wallet for fee
        _, house_pubkey = await get_or_create_wallet("HOUSE")

        # Get WORLD wallet for fee payment
        world_keypair, _ = await get_or_create_wallet("WORLD")

        # Convert revenue amount to token units
        revenue_units = int(revenue_amount * (10 ** decimals))

        # Create instruction list
        instructions = []

        # Add memo instruction if game type and ID are provided
        if game_type and game_id:
            # Create memo with format "{game_type}|{game_id}|e" (e for end)
            memo_text = f"{game_type}|{game_id}|e"
            memo_ix = create_memo_instruction(memo_text, world_keypair.pubkey())
            instructions.append(memo_ix)
            log_message("INFO", f"Added memo instruction with text: {memo_text}")

        # Get token accounts
        escrow_token_account = get_ata(escrow_pubkey, mint_pubkey)
        house_token_account = get_ata(house_pubkey, mint_pubkey)

        # Check if house token account exists
        house_account_info = await mainnet_client.get_account_info(house_token_account)
        if not house_account_info.value:
            # Add instruction to create the token account
            create_house_ata_ix = create_associated_token_account(
                payer=world_keypair.pubkey(),
                owner=house_pubkey,
                mint=mint_pubkey
            )
            instructions.append(create_house_ata_ix)

        # Add revenue transfer instruction if needed
        if revenue_amount > 0:
            revenue_ix = transfer_checked(
                TransferCheckedParams(
                    program_id=TOKEN_PROGRAM_ID,
                    source=escrow_token_account,
                    mint=mint_pubkey,
                    dest=house_token_account,
                    owner=escrow_pubkey,
                    amount=revenue_units,
                    decimals=decimals,
                    signers=[]
                )
            )
            instructions.append(revenue_ix)

        # Track created ATAs to avoid duplicates
        created_atas = set()

        # Add winner transfer instructions
        for winner in winners:
            winner_address = winner.get("wallet_address")
            winner_amount = winner.get("amount", 0)

            if not winner_address or winner_amount <= 0:
                continue

            winner_pubkey = Pubkey.from_string(winner_address)
            winner_token_account = get_ata(winner_pubkey, mint_pubkey)
            winner_amount_units = int(winner_amount * (10 ** decimals))

            # Check if winner token account exists and hasn't been created in this transaction
            winner_account_info = await mainnet_client.get_account_info(winner_token_account)
            if not winner_account_info.value and str(winner_token_account) not in created_atas:
                # Add instruction to create the token account
                create_winner_ata_ix = create_associated_token_account(
                    payer=world_keypair.pubkey(),
                    owner=winner_pubkey,
                    mint=mint_pubkey
                )
                instructions.append(create_winner_ata_ix)
                created_atas.add(str(winner_token_account))

            # Add transfer instruction
            winner_ix = transfer_checked(
                TransferCheckedParams(
                    program_id=TOKEN_PROGRAM_ID,
                    source=escrow_token_account,
                    mint=mint_pubkey,
                    dest=winner_token_account,
                    owner=escrow_pubkey,
                    amount=winner_amount_units,
                    decimals=decimals,
                    signers=[]
                )
            )
            instructions.append(winner_ix)



        log_message("INFO", f"Distributing prizes from ESCROW wallet for lobby {lobby_id}")
        tx_signature, success = await send_and_confirm_transaction_mainnet(instructions, [world_keypair, escrow_keypair])

        if success:
            log_message("INFO", f"Successfully distributed prizes from ESCROW. Signature: {tx_signature}")
            log_message("DEBUG", f"Return value type: {type(tx_signature)}, value: {tx_signature}")

            # Record the transaction in the database
            transaction_record = {
                "type": "escrow_withdrawal",
                "lobby_id": lobby_id,
                "token_symbol": token_symbol,
                "winners": [{"wallet_address": w.get("wallet_address"), "amount": w.get("amount", 0)} for w in winners],
                "revenue_amount": revenue_amount,
                "tx_signature": tx_signature,
                "created_at": datetime.datetime.now(),
                "status": "completed"
            }

            # Insert into the database
            await db["lobby_transactions"].insert_one(transaction_record)

            return tx_signature, success
        else:
            log_message("WARNING", "No transfer instructions to process")
            return "", True  # Return success if there's nothing to do
    except Exception as e:
        log_message("ERROR", f"Error distributing from ESCROW: {str(e)}")
        return "", False

async def send_tokens(sender_wallet_address: str, recipient_address: str, token_symbol: str, amount: float, reference: str = None) -> Tuple[str, bool]:
    """
    Send tokens from one wallet to another

    Args:
        sender_wallet_address: Sender's wallet address
        recipient_address: Recipient's wallet address
        token_symbol: Token symbol (e.g., "SOL", "BONK")
        amount: Amount to transfer
        reference: Optional reference for the transaction (for tracking purposes)

    Returns:
        Tuple of (signature, success)
    """
    try:
        # Get token info
        token_info = await db["tokens"].find_one({"symbol": token_symbol})
        if not token_info:
            log_message("ERROR", f"Token {token_symbol} not found")
            return "", False

        # Get token mint address (always use mainnet address)
        mint_pubkey = Pubkey.from_string(token_info["address"])
        decimals = token_info["decimals"]

        # Get WORLD wallet for fee payment
        world_keypair, _ = await get_or_create_wallet("WORLD")

        # Determine sender keypair
        sender_keypair = None

        # Check if sender is WORLD wallet
        if sender_wallet_address == str(world_keypair.pubkey()):
            log_message("INFO", f"Using WORLD wallet keypair for sender {sender_wallet_address}")
            sender_keypair = world_keypair
        else:
            # Try to get sender from users collection
            user = await db["users"].find_one({"wallet_address": sender_wallet_address})
            if user and "private_key" in user:
                sender_keypair = Keypair.from_base58_string(user["private_key"])
            else:
                # Try to get from wallets collection as fallback
                wallet = await wallets_collection.find_one({"address": sender_wallet_address})
                if wallet and "private_key" in wallet:
                    sender_keypair = Keypair.from_base58_string(wallet["private_key"])
                else:
                    log_message("ERROR", f"Sender wallet {sender_wallet_address} not found in any collection")
                    return "", False

        # Convert amount to token units
        amount_units = int(amount * (10 ** decimals))

        # Create instruction list
        instructions = []

        # Get token accounts
        sender_pubkey = Pubkey.from_string(sender_wallet_address)
        recipient_pubkey = Pubkey.from_string(recipient_address)
        sender_token_account = get_ata(sender_pubkey, mint_pubkey)
        recipient_token_account = get_ata(recipient_pubkey, mint_pubkey)



        # Track created ATAs to avoid duplicates
        created_atas = set()

        # Check if recipient token account exists
        recipient_account_info = await mainnet_client.get_account_info(recipient_token_account)
        if not recipient_account_info.value and str(recipient_token_account) not in created_atas:
            # Add instruction to create the token account
            create_ata_ix = create_associated_token_account(
                payer=world_keypair.pubkey(),
                owner=recipient_pubkey,
                mint=mint_pubkey
            )
            instructions.append(create_ata_ix)
            created_atas.add(str(recipient_token_account))

        # Add transfer instruction
        transfer_ix = transfer_checked(
            TransferCheckedParams(
                program_id=TOKEN_PROGRAM_ID,
                source=sender_token_account,
                mint=mint_pubkey,
                dest=recipient_token_account,
                owner=sender_pubkey,
                amount=amount_units,
                decimals=decimals,
                signers=[]
            )
        )
        instructions.append(transfer_ix)

        # Send transaction
        tx_signature, success = await send_and_confirm_transaction_mainnet(instructions, [world_keypair, sender_keypair])

        # Log with reference if provided
        if reference:
            log_message("INFO", f"Sent {amount} {token_symbol} from {sender_wallet_address} to {recipient_address} (Reference: {reference})")
        else:
            log_message("INFO", f"Sent {amount} {token_symbol} from {sender_wallet_address} to {recipient_address}")

        return tx_signature, success
    except Exception as e:
        log_message("ERROR", f"Error sending tokens: {str(e)}")
        return "", False

async def check_sol_balance(wallet_address: str) -> float:
    """
    Get SOL balance for a wallet address using mainnet RPC

    Args:
        wallet_address: The wallet address to check

    Returns:
        float: The SOL balance
    """
    try:
        # Use mainnet RPC directly to get SOL balance
        pubkey = Pubkey.from_string(wallet_address)
        balance_response = await mainnet_client.get_balance(pubkey)
        if hasattr(balance_response, 'value'):
            # Convert lamports to SOL
            return float(balance_response.value) / 1e9
        return 0
    except Exception as e:
        log_message("ERROR", f"Error getting SOL balance: {str(e)}")
        return 0

async def get_quote(input_mint: str, output_mint: str, amount: int, slippage: int = 1) -> dict:
    """
    Fetch a swap quote from Jupiter API.

    Args:
        input_mint: Input token mint address
        output_mint: Output token mint address
        amount: Amount to swap in token units
        slippage: Slippage tolerance in percentage (default: 1%)

    Returns:
        dict: Swap quote response
    """
    try:
        # Make sure we have a swap amount
        if amount is None:
            return {"error": "missing_amount"}

        # Build the URL with query parameters
        url = (
            f"https://api.jup.ag/swap/v1/quote?inputMint={input_mint}"
            f"&outputMint={output_mint}&amount={amount}&slippageBps={slippage * 100}"
            f"&restrictIntermediateTokens=true&platformFeeBps=100"
        )

        # Define headers with User-Agent
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json"
        }

        # Use a timeout to prevent hanging requests
        timeout = aiohttp.ClientTimeout(total=30)  # 30 second timeout

        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    log_message("ERROR", f"Jupiter quote error: {response.status} - {error_text}")
                    return {"error": f"Quote failed: {error_text}"}
    except Exception as e:
        log_message("ERROR", f"Error getting swap quote: {str(e)}")
        return {"error": f"Error getting swap quote: {str(e)}"}

async def fetch_alt_accounts(keys: List[str], client: AsyncClient) -> List[AddressLookupTableAccount]:
    """
    Fetch Address Lookup Table accounts from Solana RPC.

    Args:
        keys: List of Address Lookup Table account keys
        client: AsyncClient instance

    Returns:
        List of AddressLookupTableAccount objects
    """
    alt_accounts = []
    for key in keys:
        try:
            resp = await client.get_account_info_json_parsed(Pubkey.from_string(key))
            if resp.value and resp.value.data and hasattr(resp.value.data, 'parsed'):
                addresses = resp.value.data.parsed["info"]["addresses"]
                alt_accounts.append(
                    AddressLookupTableAccount(
                        key=Pubkey.from_string(key),
                        addresses=[Pubkey.from_string(address) for address in addresses]
                    )
                )
            else:
                log_message("WARNING", f"Could not parse ALT account data for {key}")
        except Exception as e:
            log_message("ERROR", f"Error fetching ALT account {key}: {str(e)}")
    return alt_accounts

def deserialize_instruction(instr: Dict) -> SoldersInstruction:
    """
    Deserialize a Jupiter instruction dict to a solders Instruction.

    Args:
        instr: Instruction data from Jupiter API

    Returns:
        SoldersInstruction: Deserialized instruction
    """
    return SoldersInstruction(
        program_id=Pubkey.from_string(instr["programId"]),
        accounts=[
            AccountMeta(
                pubkey=Pubkey.from_string(acc["pubkey"]),
                is_signer=acc["isSigner"],
                is_writable=acc["isWritable"]
            ) for acc in instr["accounts"]
        ],
        data=base64.b64decode(instr["data"])
    )

async def get_swap_quote(input_mint: str, output_mint: str, amount: int = None, taker: str = None) -> dict:
    """
    Get a swap quote from Jupiter API (compatibility wrapper for the old function)

    Args:
        input_mint: Input token mint address (e.g., SOL mint)
        output_mint: Output token mint address (e.g., BONK mint)
        amount: Amount to swap in token units
        taker: Optional wallet address of the taker (not used in new implementation)

    Returns:
        dict: Swap quote response formatted for compatibility
    """
    # Call the new get_quote function
    quote_response = await get_quote(input_mint, output_mint, amount)

    if "error" in quote_response:
        return quote_response

    # Generate a request ID for compatibility
    request_id = str(uuid.uuid4())

    # Return in a format compatible with the old function
    return {
        "requestId": request_id,
        "transaction": quote_response.get("swapTransaction"),
        "quote": quote_response
    }

async def execute_swap(signed_transaction: str, request_id: str, max_retries: int = 3) -> dict:
    """
    Execute a swap transaction by sending it directly to the Solana network

    Args:
        signed_transaction: Base64-encoded signed transaction
        request_id: Request ID (not used in new implementation)
        max_retries: Maximum number of retry attempts (default: 3)

    Returns:
        Dict with swap result
    """
    retry_count = 0
    backoff_time = 1  # Start with 1 second backoff

    while retry_count <= max_retries:
        try:
            # Decode the transaction
            tx_bytes = base64.b64decode(signed_transaction)

            # Send the transaction directly to the Solana network
            response = await mainnet_client.send_raw_transaction(
                tx_bytes,
                opts=TxOpts(skip_preflight=False, max_retries=0, preflight_commitment=Confirmed)
            )

            if hasattr(response, 'value'):
                signature = str(response.value)
                log_message("INFO", f"Swap transaction sent: {signature}")

                # Get recent blockhash for confirmation
                blockhash_resp = await mainnet_client.get_latest_blockhash()

                # Wait for confirmation with last_valid_block_height
                confirmation = await mainnet_client.confirm_transaction(
                    {
                        "signature": signature,
                        "blockhash": str(blockhash_resp.value.blockhash),
                        "last_valid_block_height": blockhash_resp.value.last_valid_block_height
                    },
                    commitment=Confirmed
                )

                if confirmation.value:
                    log_message("INFO", f"Swap transaction confirmed: {signature}")
                    return {
                        "success": True,
                        "signature": signature,
                        "status": "confirmed"
                    }
                else:
                    log_message("ERROR", f"Swap transaction not confirmed: {signature}")
                    return {
                        "success": False,
                        "error": "Transaction not confirmed",
                        "signature": signature
                    }
            else:
                log_message("ERROR", f"Error sending swap transaction: {response}")
                return {"error": f"Error sending transaction: {response}"}

        except Exception as e:
            log_message("ERROR", f"Error executing swap: {str(e)}")

            # If it's a network-related error, we might want to retry
            if isinstance(e, (aiohttp.ClientError, asyncio.TimeoutError)):
                if retry_count < max_retries:
                    retry_count += 1
                    log_message("INFO", f"Retrying swap execution in {backoff_time} seconds... (Attempt {retry_count}/{max_retries})")
                    await asyncio.sleep(backoff_time)
                    backoff_time *= 2  # Exponential backoff
                else:
                    return {"error": f"Error executing swap after multiple retries: {str(e)}"}
            else:
                # For other types of errors, don't retry
                return {"error": f"Error executing swap: {str(e)}"}

    # If we somehow get here, return an error
    return {"error": "Failed to execute swap after multiple retries"}

async def sign_swap_transaction(transaction_base64: str, private_key: str) -> str:
    """
    Sign a swap transaction with the user's private key

    Args:
        transaction_base64: Base64-encoded transaction from Jupiter API
        private_key: User's private key in base58 format

    Returns:
        Base64-encoded signed transaction
    """
    try:
        # Create keypair from private key
        user_keypair = Keypair.from_base58_string(private_key)

        # Get WORLD wallet for fee payer
        world_keypair, _ = await get_or_create_wallet("WORLD")

        # Get HOUSE wallet for fee account (used in the swap instructions)
        house_keypair, _ = await get_or_create_wallet("HOUSE")

        # Log for debugging
        log_message("INFO", f"Signing transaction with user keypair: {user_keypair.pubkey()}")
        log_message("INFO", f"Using WORLD keypair as fee payer: {world_keypair.pubkey()}")
        log_message("INFO", f"Using HOUSE keypair for fee account: {house_keypair.pubkey()}")

        # Decode the transaction from base64
        transaction_bytes = base64.b64decode(transaction_base64)

        # Deserialize the transaction
        raw_transaction = VersionedTransaction.from_bytes(transaction_bytes)

        # Find the index of the user's public key in the account keys
        account_keys = raw_transaction.message.account_keys
        wallet_pubkey = user_keypair.pubkey()

        try:
            # Find the index of the wallet's public key in the account keys
            wallet_index = -1
            for i, key in enumerate(account_keys):
                if key == wallet_pubkey:
                    wallet_index = i
                    break

            if wallet_index == -1:
                log_message("ERROR", f"Wallet public key not found in transaction account keys")
                return {"error": "Wallet public key not found in transaction"}

            log_message("INFO", f"Found wallet at index {wallet_index} in account keys")

            # Create a list of signers where only the user's keypair is a real signer
            # All other positions should be None
            signers = []
            for i in range(len(raw_transaction.signatures)):
                if i == wallet_index:
                    signers.append(user_keypair)
                else:
                    signers.append(None)

            # Create a new transaction with the original message and our signers
            signed_transaction = VersionedTransaction(raw_transaction.message, signers)

            # Encode the signed transaction back to base64
            signed_tx_bytes = bytes(signed_transaction)
            signed_tx_base64 = base64.b64encode(signed_tx_bytes).decode('utf-8')

            log_message("INFO", f"Successfully signed transaction, length: {len(signed_tx_bytes)} bytes")

            return signed_tx_base64
        except Exception as e:
            log_message("ERROR", f"Error signing swap transaction: {str(e)}")
            return {"error": f"Error signing swap transaction: {str(e)}"}
    except Exception as e:
        log_message("ERROR", f"Error signing transaction: {str(e)}")
        # Return the error instead of raising it
        return {"error": str(e)}

async def send_and_confirm_transaction_mainnet(instructions, signers):
    """
    Sign and send a transaction on mainnet with proper priority fee estimation

    Args:
        instructions: List of instructions to include in the transaction
        signers: List of keypairs to sign the transaction

    Returns:
        Tuple of (signature, success)
    """
    try:
        # Make sure we have the WORLD wallet as the first signer (fee payer)
        # If the first signer is not the WORLD wallet, get it and add it
        world_keypair = None

        # Check if the first signer is already the WORLD wallet
        if signers and signers[0].pubkey() == WORLD_KEYPAIR.pubkey():
            world_keypair = signers[0]
        else:
            # Get WORLD wallet
            world_keypair, _ = await get_or_create_wallet("WORLD")

            # If WORLD wallet is not in signers, add it as the first signer
            if not any(signer.pubkey() == world_keypair.pubkey() for signer in signers):
                signers = [world_keypair] + signers

        # Check if compute unit price instruction is already in the list
        compute_budget_program_id = Pubkey.from_string("ComputeBudget111111111111111111111111111111")
        has_compute_price = False

        for ix in instructions:
            if hasattr(ix, 'program_id') and ix.program_id == compute_budget_program_id:
                # Check instruction data to determine if it's a compute price instruction
                if len(ix.data) > 0 and ix.data[0] == 1:  # ComputeBudgetInstruction::SetComputeUnitPrice
                    has_compute_price = True

        # Convert all instructions to solders format
        solders_instructions = []

        # Get priority fee estimate from Helius API
        priority_fee = await get_priority_fee_estimate()

        # Add compute unit price instruction if not already present
        if not has_compute_price:
            compute_price_ix = set_compute_unit_price(priority_fee)
            solders_instructions.append(compute_price_ix)
            log_message("INFO", f"Using priority fee: {priority_fee}")

        # Add the rest of the instructions
        for instruction in instructions:
            if not isinstance(instruction, SoldersInstruction):
                # Extract program_id, accounts, and data
                program_id = instruction.program_id
                keys = [
                    AccountMeta(
                        pubkey=account_meta.pubkey,
                        is_signer=account_meta.is_signer,
                        is_writable=account_meta.is_writable
                    )
                    for account_meta in instruction.keys
                ]
                data = instruction.data
                # Create solders instruction
                instruction = SoldersInstruction(program_id, data, keys)
            solders_instructions.append(instruction)

        # Get recent blockhash from mainnet
        blockhash_resp = await mainnet_client.get_latest_blockhash()
        blockhash = Hash.from_string(str(blockhash_resp.value.blockhash))
        last_valid_block_height = blockhash_resp.value.last_valid_block_height

        # Create message and transaction with WORLD wallet as fee payer
        message = Message.new_with_blockhash(
            solders_instructions,
            world_keypair.pubkey(),  # WORLD wallet as fee payer
            blockhash
        )

        tx = Transaction(signers, message, blockhash)

        # Send transaction to mainnet using Staked Helius RPC
        response = await mainnet_client.send_raw_transaction(
            bytes(tx),
            opts=TxOpts(skip_preflight=False, max_retries=0, preflight_commitment=Confirmed)
        )
        tx_signature = response.value

        # Wait for confirmation with last_valid_block_height
        confirmation = await mainnet_client.confirm_transaction(
            {
                "signature": tx_signature,
                "blockhash": str(blockhash),
                "last_valid_block_height": last_valid_block_height
            },
            commitment=Confirmed
        )

        if confirmation.value:
            log_message("INFO", f"Mainnet transaction confirmed: {tx_signature}")
            return str(tx_signature), True
        else:
            log_message("ERROR", f"Mainnet transaction not confirmed: {tx_signature}")
            return str(tx_signature), False
    except Exception as e:
        log_message("ERROR", f"Error sending mainnet transaction: {str(e)}")
        return str(e), False

async def process_withdrawal(user_id: int, sender_wallet_address: str, sender_private_key: str,
                            recipient_address: str, token_symbol: str, amount: float) -> Dict:
    """
    Process a withdrawal directly from user's wallet to the recipient address.
    The WORLD wallet is used as the fee payer for the transaction.

    Args:
        user_id: User's Telegram ID
        sender_wallet_address: User's wallet address
        sender_private_key: User's wallet private key
        recipient_address: Recipient's wallet address (on mainnet)
        token_symbol: Token symbol (e.g., "SOL", "BONK")
        amount: Amount to withdraw

    Returns:
        Dict with withdrawal information
    """
    if not WITHDRAWALS_ENABLED:
        log_message("WARNING", f"Withdrawals are currently disabled. User {user_id} attempted to withdraw {amount} {token_symbol}")
        return {
            "success": False,
            "error": "Withdrawals are currently disabled"
        }

    try:
        # Calculate fee amount
        fee_percentage = WITHDRAWAL_FEE_PERCENT / 100.0
        fee_amount = amount * fee_percentage
        recipient_amount = amount - fee_amount

        # Create a withdrawal record with pending status
        withdrawal_id = str(uuid.uuid4())
        withdrawal_record = {
            "_id": withdrawal_id,
            "user_id": user_id,
            "sender_address": sender_wallet_address,
            "recipient_address": recipient_address,
            "token_symbol": token_symbol,
            "amount": amount,
            "fee_percentage": WITHDRAWAL_FEE_PERCENT,
            "fee_amount": fee_amount,
            "recipient_amount": recipient_amount,
            "status": "pending",
            "created_at": datetime.datetime.now(),
            "tx": None,
            "tx_status": "pending"
        }

        # Insert the withdrawal record
        await withdrawals_collection.insert_one(withdrawal_record)

        # Get wallets
        world_keypair, world_pubkey = await get_or_create_wallet("WORLD")
        _, house_pubkey = await get_or_create_wallet("HOUSE")

        # Get token info
        token_info = await db["tokens"].find_one({"symbol": token_symbol})
        if not token_info:
            log_message("ERROR", f"Token {token_symbol} not found")
            await withdrawals_collection.update_one(
                {"_id": withdrawal_id},
                {"$set": {
                    "status": "failed",
                    "tx_status": "failed",
                    "error": f"Token {token_symbol} not found"
                }}
            )
            return {
                "success": False,
                "error": f"Token {token_symbol} not found"
            }

        # Get token mint address and decimals
        mint_pubkey = Pubkey.from_string(token_info["address"])
        decimals = token_info["decimals"]

        # Convert amounts to token units
        recipient_amount_units = int(recipient_amount * (10 ** decimals))
        fee_amount_units = int(fee_amount * (10 ** decimals))

        # Create instruction list
        instructions = []

        # Get token accounts
        sender_pubkey = Pubkey.from_string(sender_wallet_address)
        sender_keypair = Keypair.from_base58_string(sender_private_key)
        sender_token_account = get_ata(sender_pubkey, mint_pubkey)
        house_token_account = get_ata(house_pubkey, mint_pubkey)
        recipient_pubkey = Pubkey.from_string(recipient_address)
        recipient_token_account = get_ata(recipient_pubkey, mint_pubkey)

        # Check if house token account exists
        house_account_info = await mainnet_client.get_account_info(house_token_account)
        if not house_account_info.value:
            # Add instruction to create the token account
            create_house_ata_ix = create_associated_token_account(
                payer=world_pubkey,
                owner=house_pubkey,
                mint=mint_pubkey
            )
            instructions.append(create_house_ata_ix)

        # Check if recipient token account exists
        recipient_account_info = await mainnet_client.get_account_info(recipient_token_account)
        if not recipient_account_info.value:
            # Add instruction to create the token account
            create_recipient_ata_ix = create_associated_token_account(
                payer=world_pubkey,
                owner=recipient_pubkey,
                mint=mint_pubkey
            )
            instructions.append(create_recipient_ata_ix)

        # Add transfer instruction for recipient (amount minus fee)
        recipient_transfer_ix = transfer_checked(
            TransferCheckedParams(
                program_id=TOKEN_PROGRAM_ID,
                source=sender_token_account,
                mint=mint_pubkey,
                dest=recipient_token_account,
                owner=sender_pubkey,
                amount=recipient_amount_units,
                decimals=decimals,
                signers=[]
            )
        )
        instructions.append(recipient_transfer_ix)

        # Add transfer instruction for fee (to house wallet)
        if fee_amount_units > 0:
            fee_transfer_ix = transfer_checked(
                TransferCheckedParams(
                    program_id=TOKEN_PROGRAM_ID,
                    source=sender_token_account,
                    mint=mint_pubkey,
                    dest=house_token_account,
                    owner=sender_pubkey,
                    amount=fee_amount_units,
                    decimals=decimals,
                    signers=[]
                )
            )
            instructions.append(fee_transfer_ix)

        # Send transaction with WORLD wallet as fee payer
        tx_signature, success = await send_and_confirm_transaction_mainnet(
            instructions, [world_keypair, sender_keypair]
        )

        # Update withdrawal record with transaction result
        await withdrawals_collection.update_one(
            {"_id": withdrawal_id},
            {"$set": {
                "tx": tx_signature,
                "tx_status": "success" if success else "failed",
                "status": "completed" if success else "failed",
                "updated_at": datetime.datetime.now()
            }}
        )

        return {
            "success": success,
            "withdrawal_id": withdrawal_id,
            "mainnet_tx": tx_signature,  # Keep mainnet_tx for backward compatibility
            "error": None if success else "Failed to transfer tokens"
        }
    except Exception as e:
        log_message("ERROR", f"Error processing withdrawal: {str(e)}")

        # Update withdrawal record with error
        if 'withdrawal_id' in locals():
            await withdrawals_collection.update_one(
                {"_id": withdrawal_id},
                {"$set": {
                    "status": "failed",
                    "tx_status": "failed",
                    "error": str(e),
                    "updated_at": datetime.datetime.now()
                }}
            )

            return {
                "success": False,
                "withdrawal_id": withdrawal_id,
                "error": str(e)
            }
        else:
            return {
                "success": False,
                "error": str(e)
            }

async def retry_mainnet_withdrawal(withdrawal_id: str) -> Dict:
    """
    Retry a failed withdrawal

    Args:
        withdrawal_id: ID of the withdrawal to retry

    Returns:
        Dict with retry result
    """
    if not WITHDRAWALS_ENABLED:
        log_message("WARNING", f"Withdrawals are currently disabled. Attempted to retry withdrawal {withdrawal_id}")
        return {
            "success": False,
            "error": "Withdrawals are currently disabled"
        }

    try:
        # Get the withdrawal record
        withdrawal = await withdrawals_collection.find_one({"_id": withdrawal_id})
        if not withdrawal:
            return {
                "success": False,
                "error": f"Withdrawal {withdrawal_id} not found"
            }

        # Check if the withdrawal is in the correct state to retry
        if withdrawal["status"] != "failed":
            return {
                "success": False,
                "error": f"Withdrawal {withdrawal_id} is not in a state that can be retried (status: {withdrawal['status']})"
            }

        # Get token info
        token_symbol = withdrawal["token_symbol"]
        token_info = await db["tokens"].find_one({"symbol": token_symbol})
        if not token_info:
            return {
                "success": False,
                "error": f"Token {token_symbol} not found"
            }

        # Get user information to get wallet details
        user_id = withdrawal["user_id"]
        user = await db["users"].find_one({"telegram_id": user_id})
        if not user or not user.get("wallet_address") or not user.get("private_key"):
            return {
                "success": False,
                "error": f"User wallet information not found for user {user_id}"
            }

        # Get wallet details
        sender_wallet_address = user["wallet_address"]
        sender_private_key = user["private_key"]
        recipient_address = withdrawal["recipient_address"]
        amount = withdrawal["amount"]

        # Process the withdrawal again
        result = await process_withdrawal(
            user_id=user_id,
            sender_wallet_address=sender_wallet_address,
            sender_private_key=sender_private_key,
            recipient_address=recipient_address,
            token_symbol=token_symbol,
            amount=amount
        )

        # Update retry count
        if "withdrawal_id" in result:
            await withdrawals_collection.update_one(
                {"_id": result["withdrawal_id"]},
                {"$set": {
                    "retry_count": withdrawal.get("retry_count", 0) + 1
                }}
            )

        return result
    except Exception as e:
        log_message("ERROR", f"Error retrying withdrawal: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

async def get_pending_withdrawals() -> List[Dict]:
    """
    Get all pending withdrawals

    Returns:
        List of pending withdrawal records
    """
    try:
        # Get all withdrawals with failed status
        pending = await withdrawals_collection.find({
            "status": "failed"
        }).to_list(length=None)

        return pending
    except Exception as e:
        log_message("ERROR", f"Error getting pending withdrawals: {str(e)}")
        return []

async def toggle_withdrawals(enabled: bool) -> bool:
    """
    Enable or disable withdrawals

    Args:
        enabled: True to enable withdrawals, False to disable

    Returns:
        True if successful, False otherwise
    """
    try:
        # Update the global variable
        global WITHDRAWALS_ENABLED
        WITHDRAWALS_ENABLED = enabled

        # Update the config in the database
        await db["config"].update_one(
            {"key": "withdrawals_enabled"},
            {"$set": {"value": enabled}},
            upsert=True
        )

        log_message("INFO", f"Withdrawals {'enabled' if enabled else 'disabled'}")
        return True
    except Exception as e:
        log_message("ERROR", f"Error toggling withdrawals: {str(e)}")
        return False

async def get_mainnet_token_balance(wallet_address: str, token_symbol: str) -> float:
    """
    Get token balance for a wallet address on mainnet

    Args:
        wallet_address: The wallet address to check
        token_symbol: Token symbol (e.g., "SOL", "BONK")

    Returns:
        float: The token balance
    """
    try:
        # Get token info
        token_info = await db["tokens"].find_one({"symbol": token_symbol})
        if not token_info:
            log_message("ERROR", f"Token {token_symbol} not found in database")
            return 0

        # Get token mint address for mainnet
        mint_pubkey = Pubkey.from_string(token_info["address"])
        decimals = token_info["decimals"]

        # Get wallet pubkey
        pubkey = Pubkey.from_string(wallet_address)
        ata = get_ata(pubkey, mint_pubkey)

        # First check if the token account exists on mainnet
        account_info = await mainnet_client.get_account_info(ata, commitment=Confirmed)
        if not account_info.value:
            # Token account doesn't exist, which means balance is 0
            return 0

        # Get account balance on mainnet
        balance_response = await mainnet_client.get_token_account_balance(ata, commitment=Confirmed)
        if hasattr(balance_response, 'value') and balance_response.value:
            ui_amount = balance_response.value.ui_amount
            if ui_amount is not None:
                return float(ui_amount)

            # If ui_amount is None, calculate from raw amount
            amount = float(balance_response.value.amount) / (10 ** decimals)
            return amount
        return 0
    except Exception as e:
        log_message("ERROR", f"Error getting mainnet token balance: {str(e)}")
        return 0

async def transfer_to_world_wallet_mainnet(sender_address: str, sender_private_key: str, token_symbol: str, amount: float) -> Tuple[str, bool]:
    """
    Transfer tokens from a wallet to the WORLD wallet on mainnet

    Args:
        sender_address: Sender's wallet address
        sender_private_key: Sender's wallet private key
        token_symbol: Token symbol (e.g., "SOL", "BONK")
        amount: Amount to transfer

    Returns:
        Tuple of (signature, success)
    """
    try:
        # Get token info
        token_info = await db["tokens"].find_one({"symbol": token_symbol})
        if not token_info:
            log_message("ERROR", f"Token {token_symbol} not found")
            return "", False

        # Get mainnet token address
        mainnet_mint_pubkey = Pubkey.from_string(token_info["address"])
        decimals = token_info["decimals"]

        # Get WORLD wallet
        world_keypair, world_pubkey = await get_or_create_wallet("WORLD")

        # Convert amount to token units
        amount_units = int(amount * (10 ** decimals))

        # Create instruction list for mainnet transaction
        mainnet_instructions = []

        # Get token accounts on mainnet
        sender_pubkey = Pubkey.from_string(sender_address)
        sender_keypair = Keypair.from_base58_string(sender_private_key)
        sender_token_account = get_ata(sender_pubkey, mainnet_mint_pubkey)
        world_token_account = get_ata(world_pubkey, mainnet_mint_pubkey)

        # Check if WORLD token account exists on mainnet
        try:
            world_account_info = await mainnet_client.get_account_info(world_token_account)
            if not world_account_info.value:
                # Add instruction to create the token account
                create_world_ata_ix = create_associated_token_account(
                    payer=world_pubkey,
                    owner=world_pubkey,
                    mint=mainnet_mint_pubkey
                )
                mainnet_instructions.append(create_world_ata_ix)
        except Exception as e:
            log_message("WARNING", f"Error checking WORLD token account on mainnet: {str(e)}")
            # We'll try to create it anyway
            create_world_ata_ix = create_associated_token_account(
                payer=world_pubkey,
                owner=world_pubkey,
                mint=mainnet_mint_pubkey
            )
            mainnet_instructions.append(create_world_ata_ix)

        # Add transfer instruction
        transfer_ix = transfer_checked(
            TransferCheckedParams(
                program_id=TOKEN_PROGRAM_ID,
                source=sender_token_account,
                mint=mainnet_mint_pubkey,
                dest=world_token_account,
                owner=sender_pubkey,
                amount=amount_units,
                decimals=decimals,
                signers=[]
            )
        )
        mainnet_instructions.append(transfer_ix)

        # Send mainnet transaction
        tx_signature, success = await send_and_confirm_transaction_mainnet(
            mainnet_instructions, [world_keypair, sender_keypair]
        )

        if success:
            log_message("INFO", f"Successfully transferred {amount} {token_symbol} from {sender_address} to WORLD wallet on mainnet")
        else:
            log_message("ERROR", f"Failed to transfer {amount} {token_symbol} from {sender_address} to WORLD wallet on mainnet")

        return tx_signature, success
    except Exception as e:
        log_message("ERROR", f"Error transferring to WORLD wallet on mainnet: {str(e)}")
        return "", False

async def get_wallet_token_balances(wallet_address: str) -> Dict:
    """
    Get all token balances for a wallet address from Jupiter API

    Args:
        wallet_address: The wallet address to check

    Returns:
        Dict with token balances and metadata
    """
    try:
        # Use Jupiter API to get token balances
        url = f"https://lite-api.jup.ag/ultra/v1/balances/{wallet_address}"

        # Define headers with User-Agent
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json"
        }

        # Use a timeout to prevent hanging requests
        timeout = aiohttp.ClientTimeout(total=30)  # 30 second timeout

        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    balances = await response.json()

                    # Filter out tokens with zero balance
                    token_mints = []
                    token_amounts = {}

                    # The response is a dict where keys are token mint addresses
                    # Example: {"SOL": {"amount": "12556635", "uiAmount": 0.012556635, "slot": 339225040, "isFrozen": false}}
                    for token_key, token_data in balances.items():
                        # Skip tokens with zero balance
                        ui_amount = token_data.get("uiAmount", 0)
                        if not ui_amount or float(ui_amount) <= 0:
                            continue

                        # Handle special case for SOL - map "SOL" to the actual SOL mint address
                        token_mint = SOL_ADDRESS if token_key == "SOL" else token_key

                        token_mints.append(token_mint)
                        token_amounts[token_mint] = float(ui_amount)

                    if not token_mints:
                        return {
                            "success": True,
                            "tokens": [],
                            "total_value_usd": 0
                        }

                    # Get token metadata and prices in batch
                    token_info = await get_tokens_metadata_and_prices(token_mints)

                    # Process tokens with metadata and prices
                    tokens_with_metadata = []
                    total_value_usd = 0

                    for token_mint in token_mints:
                        if token_mint not in token_info:
                            continue

                        info = token_info[token_mint]
                        amount = token_amounts[token_mint]
                        price_usd = info.get("price_usd", 0)
                        value_usd = amount * price_usd

                        # Only include tokens worth more than $1
                        if value_usd >= 1.0:
                            tokens_with_metadata.append({
                                "mint": token_mint,
                                "symbol": info.get("symbol", "Unknown"),
                                "name": info.get("name", "Unknown Token"),
                                "amount": amount,
                                "decimals": info.get("decimals", 9),
                                "price_usd": price_usd,
                                "value_usd": value_usd,
                                "logo": info.get("logo", "")
                            })

                            total_value_usd += value_usd

                    # Sort tokens by USD value (highest first)
                    tokens_with_metadata.sort(key=lambda x: x["value_usd"], reverse=True)

                    return {
                        "success": True,
                        "tokens": tokens_with_metadata,
                        "total_value_usd": total_value_usd
                    }
                else:
                    error_text = await response.text()
                    log_message("ERROR", f"Failed to get token balances: {response.status} - {error_text}")
                    return {"success": False, "error": f"Failed to get token balances: {response.status}"}
    except Exception as e:
        log_message("ERROR", f"Error getting wallet token balances: {str(e)}")
        return {"success": False, "error": str(e)}

async def get_tokens_metadata_and_prices(token_mints: List[str]) -> Dict[str, Dict]:
    """
    Get metadata and prices for multiple tokens using Helius API's getAssetBatch method

    Args:
        token_mints: List of token mint addresses

    Returns:
        Dict mapping token mint addresses to their metadata and price info
    """
    if not token_mints:
        return {}

    try:
        # Use Helius API to get token metadata and prices in batch
        url = STAKED_HELIUS_RPC
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

        # Prepare the request payload for getAssetBatch method
        payload = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": "getAssetBatch",
            "params": {
                "ids": token_mints
            }
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()

                    # Check if we have valid results
                    if "result" not in data or not isinstance(data["result"], list):
                        log_message("WARNING", f"Invalid response format from Helius API: {data}")
                        return {}

                    # Process each token in the batch response
                    token_info_map = {}

                    for asset in data["result"]:
                        # Get token mint address
                        token_mint = asset.get("id")
                        if not token_mint:
                            continue

                        # Get token info
                        token_info = asset.get("token_info", {})
                        symbol = token_info.get("symbol", "Unknown")
                        decimals = token_info.get("decimals", 9)

                        # Get price info
                        price_usd = 0
                        if "price_info" in token_info and "price_per_token" in token_info["price_info"]:
                            price_usd = float(token_info["price_info"]["price_per_token"])

                        # Get content for name and logo
                        content = asset.get("content", {})
                        metadata = content.get("metadata", {})
                        name = metadata.get("name", symbol)

                        # Get logo from links or files
                        logo = ""
                        links = content.get("links", {})
                        if "image" in links:
                            logo = links["image"]
                        elif "files" in content and len(content["files"]) > 0:
                            logo = content["files"][0].get("uri", "")

                        # Store token info
                        token_info_map[token_mint] = {
                            "symbol": symbol,
                            "name": name,
                            "decimals": decimals,
                            "price_usd": price_usd,
                            "logo": logo
                        }

                    return token_info_map
                else:
                    error_text = await response.text()
                    log_message("WARNING", f"Failed to get token metadata and prices: {response.status} - {error_text}")
                    return {}
    except Exception as e:
        log_message("WARNING", f"Error getting token metadata and prices: {str(e)}")
        return {}

async def get_token_metadata(token_mint: str) -> Dict:
    """
    Get token metadata from Helius API

    Args:
        token_mint: Token mint address

    Returns:
        Dict with token metadata
    """
    # Use the batch function for a single token
    token_info = await get_tokens_metadata_and_prices([token_mint])

    if token_mint in token_info:
        return {
            "symbol": token_info[token_mint].get("symbol", "Unknown"),
            "name": token_info[token_mint].get("name", "Unknown Token"),
            "decimals": token_info[token_mint].get("decimals", 9),
            "logo": token_info[token_mint].get("logo", "")
        }

    return {"symbol": "Unknown", "name": "Unknown Token", "decimals": 9, "logo": ""}

async def get_priority_fee_estimate(transaction=None):
    """
    Get priority fee estimate from Helius API

    Args:
        transaction: Optional transaction to estimate for

    Returns:
        Priority fee in micro-lamports
    """
    try:
        url = STAKED_HELIUS_RPC
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

        # Prepare the request payload
        payload = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": "getPriorityFeeEstimate",
            "params": [
                {
                    "options": {
                        "recommended": True
                    }
                }
            ]
        }

        # Add transaction if provided
        if transaction:
            payload["params"][0]["transaction"] = transaction

        # Make the request
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    if "result" in result and "priorityFeeEstimate" in result["result"]:
                        fee = result["result"]["priorityFeeEstimate"]
                        log_message("INFO", f"Got priority fee estimate from Helius API: {fee}")
                        return int(fee)
                    else:
                        log_message("WARNING", f"Unexpected response format from Helius API: {result}")
                else:
                    log_message("WARNING", f"Failed to get priority fee estimate: {response.status}")
    except Exception as e:
        log_message("WARNING", f"Error getting priority fee estimate: {str(e)}")

    # Fallback to default value if API call fails
    log_message("INFO", "Using default priority fee: 20000")
    return 20000

async def get_token_price(token_mint: str) -> float:
    """
    Get token price from Helius API

    Args:
        token_mint: Token mint address

    Returns:
        float: Token price in USD
    """
    # Use the batch function for a single token
    token_info = await get_tokens_metadata_and_prices([token_mint])

    if token_mint in token_info:
        return token_info[token_mint].get("price_usd", 0)

    return 0

async def send_sol_for_fees(recipient_address: str, amount: float = 0.005) -> Tuple[str, bool]:
    """
    Send SOL from WORLD wallet to a recipient for transaction fees on localnet

    Args:
        recipient_address: Recipient's wallet address
        amount: Amount of SOL to send (default: 0.005)

    Returns:
        Tuple of (signature, success)
    """
    try:
        # Get WORLD wallet
        world_keypair, _ = await get_or_create_wallet("WORLD")

        # Convert SOL amount to lamports
        lamports = int(amount * 1e9)

        # Create transfer instruction
        recipient_pubkey = Pubkey.from_string(recipient_address)
        transfer_ix = SoldersInstruction(
            program_id=Pubkey.from_string("********************************"),
            data=bytes([2, 0, 0, 0]) + lamports.to_bytes(8, byteorder='little'),
            accounts=[
                AccountMeta(pubkey=world_keypair.pubkey(), is_signer=True, is_writable=True),
                AccountMeta(pubkey=recipient_pubkey, is_signer=False, is_writable=True),
            ]
        )

        # Send transaction
        tx_signature, success = await send_and_confirm_transaction([transfer_ix], [world_keypair])

        if success:
            log_message("INFO", f"Sent {amount} SOL to {recipient_address} for fees on localnet")
        else:
            log_message("ERROR", f"Failed to send SOL to {recipient_address} for fees on localnet")

        return tx_signature, success
    except Exception as e:
        log_message("ERROR", f"Error sending SOL for fees on localnet: {str(e)}")
        return "", False

async def send_sol_for_fees_mainnet(recipient_address: str, amount: float = 0.005) -> Tuple[str, bool]:
    """
    Send SOL from WORLD wallet to a recipient for transaction fees on mainnet

    Args:
        recipient_address: Recipient's wallet address
        amount: Amount of SOL to send (default: 0.005)

    Returns:
        Tuple of (signature, success)
    """
    try:
        # Get WORLD wallet
        world_keypair, _ = await get_or_create_wallet("WORLD")

        # Convert SOL amount to lamports
        lamports = int(amount * 1e9)

        # Create transfer instruction
        recipient_pubkey = Pubkey.from_string(recipient_address)
        transfer_ix = SoldersInstruction(
            program_id=Pubkey.from_string("********************************"),
            data=bytes([2, 0, 0, 0]) + lamports.to_bytes(8, byteorder='little'),
            accounts=[
                AccountMeta(pubkey=world_keypair.pubkey(), is_signer=True, is_writable=True),
                AccountMeta(pubkey=recipient_pubkey, is_signer=False, is_writable=True),
            ]
        )

        # Send transaction on mainnet
        tx_signature, success = await send_and_confirm_transaction_mainnet([transfer_ix], [world_keypair])

        if success:
            log_message("INFO", f"Sent {amount} SOL to {recipient_address} for fees on mainnet")
        else:
            log_message("ERROR", f"Failed to send SOL to {recipient_address} for fees on mainnet")

        return tx_signature, success
    except Exception as e:
        log_message("ERROR", f"Error sending SOL for fees on mainnet: {str(e)}")
        return "", False

async def swap_token_to_active_direct(user_id: int, input_mint: str, amount: float, decimals: int) -> Dict:
    """
    Swap a token to the active token using Jupiter API directly

    Args:
        user_id: User's Telegram ID
        input_mint: Input token mint address
        amount: Amount to swap
        decimals: Token decimals

    Returns:
        Dict with swap result
    """
    try:
        # Get user's wallet information
        user = await db["users"].find_one({"telegram_id": user_id})
        if not user or "wallet_address" not in user or "private_key" not in user:
            return {"success": False, "error": "User wallet not found"}

        wallet_address = user["wallet_address"]
        private_key = user["private_key"]

        # Get active token mint address
        token_info = await db["tokens"].find_one({"symbol": ACTIVE_TOKEN})
        if not token_info:
            return {"success": False, "error": f"Token {ACTIVE_TOKEN} not found"}

        output_mint = token_info["address"]
        output_decimals = token_info["decimals"]

        # Handle special case for SOL
        if input_mint == "SOL":
            input_mint = SOL_ADDRESS
            log_message("INFO", f"Swapping SOL to {ACTIVE_TOKEN} - will handle ATA creation and closing")

        # Convert amount to token units
        amount_units = int(amount * (10 ** decimals))

        # Get HOUSE wallet for fee account (only need the pubkey)
        _, house_pubkey = await get_or_create_wallet("HOUSE")
        house_token_account = get_ata(house_pubkey, Pubkey.from_string(output_mint))

        # Get quote from Jupiter API
        quote = await get_quote(input_mint, output_mint, amount_units, slippage=1)
        if "error" in quote:
            return {"success": False, "error": f"Failed to get swap quote: {quote['error']}"}

        # Extract output amount from quote
        output_amount_units = int(quote.get("outAmount", 0))
        output_amount = output_amount_units / (10 ** output_decimals)

        # Get swap instructions
        user_pubkey = Pubkey.from_string(wallet_address)

        # Prepare the payload for swap instructions
        swap_instructions_payload = {
            "userPublicKey": str(user_pubkey),
            "quoteResponse": quote,
            "feeAccount": str(house_token_account),
        }

        # Get swap instructions from Jupiter API
        try:
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }

            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    "https://api.jup.ag/swap/v1/swap-instructions",
                    headers=headers,
                    json=swap_instructions_payload
                ) as response:
                    if response.status == 200:
                        swap_instructions = await response.json()
                    else:
                        error_text = await response.text()
                        log_message("ERROR", f"Failed to get swap instructions: {error_text}")
                        return {"success": False, "error": f"Failed to get swap instructions: {error_text}"}
        except Exception as e:
            log_message("ERROR", f"Error getting swap instructions: {str(e)}")
            return {"success": False, "error": f"Error getting swap instructions: {str(e)}"}

        # Get WORLD wallet for fee payer - do this BEFORE processing instructions
        world_keypair, world_pubkey = await get_or_create_wallet("WORLD")

        # Build instructions
        instructions = []

        # Add compute budget instructions
        for instr in swap_instructions.get("computeBudgetInstructions", []):
            instructions.append(deserialize_instruction(instr))

        # Process setup instructions and modify ATA creation payer if present
        setup_instructions = swap_instructions.get("setupInstructions", [])
        for instr_data in setup_instructions:
            setup_instruction = deserialize_instruction(instr_data)

            # Check if this is an ATA creation instruction
            # ATA creation instructions are from the ATA Program ID
            if setup_instruction.program_id == ASSOCIATED_TOKEN_PROGRAM_ID:
                log_message("INFO", f"Found ATA Program instruction in setup")
                # Standard create_associated_token_account instruction data is empty bytes (Base64 "")
                # createIdempotent instruction data is a single byte 1 (Base64 "AQ==")
                # Let's check for either, assuming the payer is the first account
                if (
                    setup_instruction.data == b"" or
                    setup_instruction.data == base64.b64decode("AQ==") # Base64 for [1] (createIdempotent)
                ) and len(setup_instruction.accounts) >= 1:
                    log_message("INFO", "Identified ATA creation instruction. Changing payer to WORLD wallet.")

                    # Create a new list of account metas, replacing the payer
                    modified_accounts = []
                    # The payer account is typically the first account (index 0)
                    payer_account_index = 0

                    for i, account_meta in enumerate(setup_instruction.accounts):
                        if i == payer_account_index:
                            # Create a new AccountMeta with the world_keypair's pubkey
                            modified_accounts.append(AccountMeta(
                                pubkey=world_pubkey,          # New payer pubkey
                                is_signer=account_meta.is_signer, # Keep original signer status (should be True)
                                is_writable=account_meta.is_writable # Keep original writable status (should be True)
                            ))
                            log_message("INFO", f"Modified ATA creation payer to WORLD wallet: {world_pubkey}")
                        else:
                            # Keep other accounts as they are
                            modified_accounts.append(account_meta)

                    # Create a new Instruction with the modified accounts
                    modified_setup_instruction = SoldersInstruction(
                        program_id=setup_instruction.program_id,
                        accounts=modified_accounts,
                        data=setup_instruction.data
                    )
                    instructions.append(modified_setup_instruction) # Add the modified instruction
                    continue # Move to the next setup instruction

                else:
                    log_message("INFO", "ATA Program instruction has unexpected data or accounts. Not modifying setup.")
                    # Fall through to append original instruction

            # If not an ATA creation instruction or modification failed, append the original setup instruction
            instructions.append(setup_instruction)

        # Add swap instruction
        if "swapInstruction" in swap_instructions:
            instructions.append(deserialize_instruction(swap_instructions["swapInstruction"]))
        else:
            return {"success": False, "error": "No swap instruction in response"}

        # Process cleanup instruction if present
        cleanup_instruction_data = swap_instructions.get("cleanupInstruction")
        if cleanup_instruction_data:
            cleanup_instruction = deserialize_instruction(cleanup_instruction_data)

            # Check if the cleanup instruction is a CloseAccount instruction from the Token Program
            # CloseAccount instruction data is a single byte: 9 (Base64 "CQ==")
            if (
                cleanup_instruction.program_id == TOKEN_PROGRAM_ID and
                cleanup_instruction.data == base64.b64decode("CQ==")
            ):
                log_message("INFO", "Found SPL Token CloseAccount instruction in cleanup.")
                # The accounts for CloseAccount are typically:
                # 0: Account to close (Writable)
                # 1: Destination for SOL (Writable)
                # 2: Owner (Signer)

                if len(cleanup_instruction.accounts) >= 2:
                    # Create a new list of account metas, replacing the destination
                    modified_accounts = []
                    # The destination account is typically the second account (index 1)
                    destination_account_index = 1

                    for i, account_meta in enumerate(cleanup_instruction.accounts):
                        if i == destination_account_index:
                            # Create a new AccountMeta with the world_keypair's pubkey
                            modified_accounts.append(AccountMeta(
                                pubkey=world_pubkey,          # New destination pubkey
                                is_signer=account_meta.is_signer,
                                is_writable=account_meta.is_writable
                            ))
                            log_message("INFO", f"Modified CloseAccount destination to WORLD wallet: {world_pubkey}")
                        else:
                            # Keep other accounts as they are
                            modified_accounts.append(account_meta)

                    # Create a new Instruction with the modified accounts
                    modified_cleanup_instruction = SoldersInstruction(
                        program_id=cleanup_instruction.program_id,
                        accounts=modified_accounts,
                        data=cleanup_instruction.data
                    )

                    instructions.append(modified_cleanup_instruction) # Add the modified instruction
                else:
                    log_message("INFO", "CloseAccount instruction has unexpected number of accounts. Not modifying cleanup.")
                    instructions.append(cleanup_instruction) # Add the original instruction
            else:
                log_message("INFO", "Cleanup instruction is not a SPL Token CloseAccount. Not modifying cleanup.")
                instructions.append(cleanup_instruction) # Add the original instruction
        else:
            log_message("INFO", "No cleanup instruction provided by Jupiter.")

        # Get address lookup tables if any
        address_lookup_table_accounts = []
        alt_keys = swap_instructions.get("addressLookupTableAddresses", [])
        if alt_keys:
            address_lookup_table_accounts = await fetch_alt_accounts(alt_keys, mainnet_client)

        # Get latest blockhash
        blockhash_resp = await mainnet_client.get_latest_blockhash()
        blockhash = blockhash_resp.value.blockhash

        # WORLD wallet for fee payer was already obtained earlier

        # Create user keypair
        user_keypair = Keypair.from_base58_string(private_key)

        # Build transaction with fee payer
        try:
            msg = MessageV0.try_compile(
                world_pubkey,
                instructions,
                address_lookup_table_accounts,
                blockhash
            )
            tx = VersionedTransaction(msg, [world_keypair, user_keypair])

            # Send transaction
            response = await mainnet_client.send_raw_transaction(
                bytes(tx),
                opts=TxOpts(
                    skip_preflight=False,
                    max_retries=10,
                    preflight_commitment=Confirmed
                )
            )
            if hasattr(response, 'value'):
                signature = response.value
                log_message("INFO", f"Swap transaction sent: {signature}")

                # Get recent blockhash for confirmation
                blockhash_resp = await mainnet_client.get_latest_blockhash()

                # Wait for confirmation with last_valid_block_height
                confirmation = await mainnet_client.confirm_transaction(
                    {
                        "signature": signature,
                        "blockhash": str(blockhash_resp.value.blockhash),
                        "last_valid_block_height": blockhash_resp.value.last_valid_block_height
                    },
                    commitment=Confirmed
                )

                if confirmation.value:
                    log_message("INFO", f"Swap transaction confirmed: {signature}")

                    # Get token prices for USD value calculation
                    input_price_usd = 0
                    output_price_usd = 0

                    if input_mint != SOL_ADDRESS:
                        input_price_usd = await get_token_price(input_mint)
                    else:
                        input_price_usd = await get_token_price(SOL_ADDRESS)

                    output_price_usd = await get_token_price(output_mint)

                    input_value_usd = amount * input_price_usd
                    output_value_usd = output_amount * output_price_usd

                    return {
                        "success": True,
                        "signature": signature,
                        "input_amount": amount,
                        "input_mint": input_mint,
                        "output_mint": output_mint,
                        "output_amount": output_amount,
                        "input_value_usd": input_value_usd,
                        "output_value_usd": output_value_usd
                    }
                else:
                    log_message("ERROR", f"Swap transaction not confirmed: {signature}")
                    return {
                        "success": False,
                        "error": "Transaction not confirmed",
                        "signature": signature
                    }
            else:
                log_message("ERROR", f"Error sending swap transaction: {response}")
                return {"success": False, "error": f"Error sending transaction: {response}"}
        except Exception as e:
            log_message("ERROR", f"Error building or sending transaction: {str(e)}")
            return {"success": False, "error": f"Error building or sending transaction: {str(e)}"}
    except Exception as e:
        log_message("ERROR", f"Error swapping token: {str(e)}")
        return {"success": False, "error": str(e)}

async def swap_token_to_active(user_id: int, input_mint: str, amount: float, decimals: int) -> Dict:
    """
    Swap a token to the active token using Jupiter API

    This is a wrapper around swap_token_to_active_direct that maintains compatibility
    with the existing code.

    Args:
        user_id: User's Telegram ID
        input_mint: Input token mint address
        amount: Amount to swap
        decimals: Token decimals

    Returns:
        Dict with swap result
    """
    # Call the new direct implementation
    return await swap_token_to_active_direct(user_id, input_mint, amount, decimals)

async def swap_all_tokens_to_active(user_id: int, progress_callback=None) -> Dict:
    """
    Swap all tokens worth more than $1 to the active token

    Args:
        user_id: User's Telegram ID
        progress_callback: Optional callback function to report progress
                          Function signature: callback(message: str)
                          This should be a regular function, not a coroutine

    Returns:
        Dict with swap results
    """
    try:
        # Get user's wallet information
        user = await db["users"].find_one({"telegram_id": user_id})
        if not user or "wallet_address" not in user:
            return {"success": False, "error": "User wallet not found"}

        wallet_address = user["wallet_address"]

        # Get token balances BEFORE sending SOL for fees
        log_message("INFO", f"Getting token balances for {wallet_address}")
        balances = await get_wallet_token_balances(wallet_address)
        if not balances["success"]:
            return {"success": False, "error": balances["error"]}

        # Get active token mint address
        token_info = await db["tokens"].find_one({"symbol": ACTIVE_TOKEN})
        if not token_info:
            return {"success": False, "error": f"Token {ACTIVE_TOKEN} not found"}

        active_token_mint = token_info["address"]

        # Filter out the active token
        tokens_to_swap = [token for token in balances["tokens"] if token["mint"] != active_token_mint]

        if not tokens_to_swap:
            return {"success": False, "error": "No tokens to swap"}

        # Check if we have SOL token in the list
        sol_token = next((token for token in tokens_to_swap if token["mint"] == SOL_ADDRESS), None)
        original_sol_balance = sol_token["amount"] if sol_token else 0
        log_message("INFO", f"Original SOL balance: {original_sol_balance} SOL")

        # Prepare tokens for swapping - put SOL at the end
        swap_results = []

        # Separate SOL and non-SOL tokens
        non_sol_tokens = [t for t in tokens_to_swap if t["mint"] != SOL_ADDRESS]

        # We'll handle SOL separately at the end
        # Non-SOL tokens first, then SOL if present

        # Report progress if callback is provided - show all tokens including SOL
        if progress_callback:
            # Create a combined list for display with SOL at the end
            display_tokens = non_sol_tokens.copy()
            if sol_token:
                display_tokens.append(sol_token)
            progress_callback("init", display_tokens)

        # First handle non-SOL tokens
        for i, token in enumerate(non_sol_tokens):
            log_message("INFO", f"Swapping {token['amount']} {token['symbol']} to {ACTIVE_TOKEN}")

            # Report swap start if callback is provided
            if progress_callback:
                try:
                    progress_callback("swap_start", {"token": token["symbol"]})
                except Exception as e:
                    log_message("WARNING", f"Error in progress callback: {str(e)}")

            # Swap the token
            swap_result = await swap_token_to_active(
                user_id,
                token["mint"],
                token["amount"],
                token["decimals"]
            )

            result = {
                "token": token["symbol"],
                "amount": token["amount"],
                "value_usd": token["value_usd"],
                "success": swap_result["success"],
                "error": swap_result.get("error"),
                "signature": swap_result.get("signature"),
                "output_amount": swap_result.get("output_amount", 0),
                "output_value_usd": swap_result.get("output_value_usd", 0),
                "output_symbol": ACTIVE_TOKEN  # Add the output token symbol
            }

            swap_results.append(result)

            # Report progress if callback is provided
            if progress_callback:
                try:
                    progress_callback("swap_result", result)
                except Exception as e:
                    log_message("WARNING", f"Error in progress callback: {str(e)}")

            # Wait a bit between swaps
            await asyncio.sleep(2)

        # Now handle SOL if present (as the last token)
        if sol_token:
            # Check the current SOL balance right before swapping
            # This ensures we're using the most up-to-date balance
            current_sol_balance = await check_sol_balance(wallet_address)

            # Use the exact current balance - no buffer
            sol_to_swap = current_sol_balance
            log_message("INFO", f"Using current SOL balance for swap: {sol_to_swap} SOL (original was {original_sol_balance} SOL)")

            # Double-check that we're not trying to swap more than we have
            if sol_to_swap <= 0:
                log_message("WARNING", f"Not enough SOL to swap: {sol_to_swap} SOL")
                sol_to_swap = 0

            if sol_to_swap > 0:
                log_message("INFO", f"Swapping {sol_to_swap} SOL to {ACTIVE_TOKEN}")

                # Report swap start if callback is provided
                if progress_callback:
                    try:
                        progress_callback("swap_start", {"token": "SOL"})
                    except Exception as e:
                        log_message("WARNING", f"Error in progress callback: {str(e)}")

                swap_result = await swap_token_to_active(
                    user_id,
                    SOL_ADDRESS,
                    sol_to_swap,
                    9  # SOL decimals
                )

                result = {
                    "token": "SOL",
                    "amount": sol_to_swap,
                    "value_usd": sol_to_swap * sol_token["price_usd"],
                    "success": swap_result["success"],
                    "error": swap_result.get("error"),
                    "signature": swap_result.get("signature"),
                    "output_amount": swap_result.get("output_amount", 0),
                    "output_value_usd": swap_result.get("output_value_usd", 0),
                    "output_symbol": ACTIVE_TOKEN  # Add the output token symbol
                }

                swap_results.append(result)

                # Report progress if callback is provided
                if progress_callback:
                    try:
                        progress_callback("swap_result", result)
                    except Exception as e:
                        log_message("WARNING", f"Error in progress callback: {str(e)}")

        # Check if any swaps have a signature but are marked as failed
        # This can happen when the Jupiter API returns an error but the transaction was actually successful
        for result in swap_results:
            if not result["success"] and "signature" in result and result["signature"]:
                # If we have a signature, consider it a success
                result["success"] = True
                result["note"] = "Transaction likely succeeded despite API error"

        # Count successful and failed swaps
        successful_swaps = sum(1 for result in swap_results if result["success"])
        failed_swaps = len(swap_results) - successful_swaps

        # Final result
        result = {
            "success": successful_swaps > 0,
            "total_swaps": len(swap_results),
            "successful_swaps": successful_swaps,
            "failed_swaps": failed_swaps,
            "swap_results": swap_results
        }

        # Report final progress if callback is provided
        if progress_callback:
            progress_callback("final", {
                "successful_swaps": successful_swaps,
                "total_swaps": len(swap_results)
            })

        # Return the result
        return result
    except Exception as e:
        log_message("ERROR", f"Error swapping all tokens: {str(e)}")
        return {"success": False, "error": str(e)}


