from pymongo import MongoClient
from solders.keypair import Keypair
from datetime import datetime
import base58

def connect_to_mongo():
    client = MongoClient("mongodb://localhost:27017/")
    db = client["solette"]  # Change to your DB name
    users = db["users"]
    keys = db["keys"]
    return users, keys

def extract_user_wallets(users_col):
    user_wallets = []
    for user in users_col.find({"wallet_address": {"$exists": True}, "private_key": {"$exists": True}}):
        user_wallets.append({
            "address": user["wallet_address"],
            "private_key": user["private_key"],
            "assigned": 1,
            "assigned_at": datetime.utcnow(),
            "assigned_to": user["telegram_id"]
        })
    return user_wallets

def generate_solana_wallets(count, starting_id):
    wallets = []
    for i in range(count):
        kp = Keypair()
        private_key_b58 = base58.b58encode(kp.to_bytes()).decode()
        wallet = {
            "_id": starting_id + i,
            "address": str(kp.pubkey()),
            "private_key": private_key_b58,
            "assigned": 0
        }
        wallets.append(wallet)
    return wallets

def build_keys_collection(users_col, keys_col):
    # Clear the keys collection
    keys_col.delete_many({})
    
    # Extract user wallets
    user_wallets = extract_user_wallets(users_col)

    # Add _id field
    for idx, wallet in enumerate(user_wallets):
        wallet["_id"] = idx + 1

    print(f"Found {len(user_wallets)} user-assigned wallets.")

    # Calculate how many more we need
    total_needed = 100
    remaining = total_needed - len(user_wallets)

    # Generate new wallets
    generated_wallets = generate_solana_wallets(remaining, starting_id=len(user_wallets) + 1)

    # Combine and insert
    all_wallets = user_wallets + generated_wallets
    keys_col.insert_many(all_wallets)
    print(f"Inserted {len(all_wallets)} wallets into 'keys' collection.")

def main():
    users_col, keys_col = connect_to_mongo()
    build_keys_collection(users_col, keys_col)

if __name__ == "__main__":
    main()

